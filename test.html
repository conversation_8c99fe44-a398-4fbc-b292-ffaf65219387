<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>有道云笔记导出助手 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 10px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #333;
            margin-top: 0;
        }
        
        .step {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .feature-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 有道云笔记导出助手</h1>
        <p>一键导出有道云笔记文件夹内容的Chrome插件</p>
    </div>

    <div class="section">
        <h2>📋 安装步骤</h2>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>开启开发者模式</strong><br>
            打开Chrome浏览器，访问 <span class="code">chrome://extensions/</span>，开启右上角的"开发者模式"
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            <strong>加载插件</strong><br>
            点击"加载已解压的扩展程序"，选择本项目的文件夹
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            <strong>验证安装</strong><br>
            在扩展程序列表中看到"有道云笔记导出助手"即表示安装成功
        </div>
    </div>

    <div class="section">
        <h2>🎯 使用方法</h2>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>打开有道云笔记</strong><br>
            访问 <a href="https://note.youdao.com/" target="_blank">https://note.youdao.com/</a> 并登录您的账号
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            <strong>选择文件夹</strong><br>
            导航到您想要导出的文件夹，确保可以看到文件夹内的笔记列表
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            <strong>启动插件</strong><br>
            点击浏览器工具栏中的插件图标，在弹出窗口中选择导出格式
        </div>
        
        <div class="step">
            <span class="step-number">4</span>
            <strong>开始导出</strong><br>
            点击"开始导出"按钮，等待导出完成并下载文件
        </div>
    </div>

    <div class="section">
        <h2>✨ 功能特点</h2>
        
        <div class="feature-list">
            <div class="feature-item">
                <span class="feature-icon">🚀</span>
                <strong>一键导出</strong><br>
                快速导出整个文件夹下的所有笔记
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">📝</span>
                <strong>多种格式</strong><br>
                支持Markdown、HTML、纯文本、JSON格式
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">📊</span>
                <strong>进度显示</strong><br>
                实时显示导出进度和状态信息
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">🎯</span>
                <strong>智能识别</strong><br>
                自动识别有道云笔记页面结构
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">💾</span>
                <strong>本地保存</strong><br>
                导出文件直接保存到本地设备
            </div>
            
            <div class="feature-item">
                <span class="feature-icon">🔒</span>
                <strong>隐私安全</strong><br>
                所有处理都在本地进行，保护隐私
            </div>
        </div>
    </div>

    <div class="section">
        <h2>⚠️ 注意事项</h2>
        
        <div class="warning">
            <strong>重要提醒：</strong>
            <ul>
                <li>请确保在有道云笔记网页版中使用此插件</li>
                <li>导出过程需要稳定的网络连接</li>
                <li>某些特殊格式的内容可能无法完美导出</li>
                <li>图片和附件需要单独保存</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>🔧 故障排除</h2>
        
        <div class="step">
            <strong>插件无法启动</strong><br>
            • 确认当前页面是有道云笔记网页版<br>
            • 刷新页面后重试<br>
            • 检查插件是否正确安装并启用
        </div>
        
        <div class="step">
            <strong>导出失败</strong><br>
            • 检查网络连接是否稳定<br>
            • 确认文件夹中有可导出的笔记<br>
            • 尝试减少单次导出的笔记数量
        </div>
        
        <div class="step">
            <strong>内容不完整</strong><br>
            • 某些动态加载的内容可能需要手动展开<br>
            • 复杂格式的笔记建议手动复制重要内容<br>
            • 图片和附件需要单独保存
        </div>
    </div>

    <div class="section">
        <h2>🔗 快速链接</h2>
        <a href="https://note.youdao.com/" target="_blank" class="btn">打开有道云笔记</a>
        <a href="chrome://extensions/" target="_blank" class="btn btn-secondary">管理扩展程序</a>
    </div>

    <div class="success">
        <strong>🎉 安装完成！</strong> 现在您可以前往有道云笔记网页版开始使用导出功能了。
    </div>
</body>
</html>
