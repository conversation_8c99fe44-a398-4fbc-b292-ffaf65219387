// background.js - 后台脚本
class YoudaoExportBackground {
  constructor() {
    this.init();
  }
  
  init() {
    // 监听插件安装
    chrome.runtime.onInstalled.addListener((details) => {
      console.log('有道云笔记导出助手已安装');
      
      if (details.reason === 'install') {
        // 首次安装时的处理
        this.showWelcomeNotification();
      }
    });
    
    // 监听来自content script和popup的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
    
    // 监听标签页更新，检测有道云笔记页面
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url && tab.url.includes('note.youdao.com')) {
        this.handleYoudaoPageLoad(tabId, tab);
      }
    });
  }
  
  showWelcomeNotification() {
    // 显示欢迎通知
    if (chrome.notifications) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: '有道云笔记导出助手',
        message: '插件已安装成功！请在有道云笔记网页版中使用。'
      });
    }
  }
  
  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'exportProgress':
          // 转发进度消息到popup
          this.forwardMessageToPopup(message);
          break;
          
        case 'exportComplete':
          // 转发完成消息到popup
          this.forwardMessageToPopup(message);
          break;
          
        case 'exportError':
          // 转发错误消息到popup
          this.forwardMessageToPopup(message);
          break;
          
        case 'downloadFile':
          // 处理文件下载
          await this.handleFileDownload(message.data);
          break;
          
        default:
          console.log('未知消息类型:', message.action);
      }
    } catch (error) {
      console.error('处理后台消息失败:', error);
    }
  }
  
  forwardMessageToPopup(message) {
    // 向所有popup发送消息
    chrome.runtime.sendMessage(message).catch(error => {
      // popup可能已关闭，忽略错误
      console.log('转发消息到popup失败（popup可能已关闭）:', error.message);
    });
  }
  
  async handleFileDownload(data) {
    try {
      // 创建下载
      const downloadId = await chrome.downloads.download({
        url: data.url,
        filename: data.filename,
        saveAs: true
      });
      
      console.log('文件下载开始:', downloadId);
      
      // 监听下载完成
      chrome.downloads.onChanged.addListener((downloadDelta) => {
        if (downloadDelta.id === downloadId && downloadDelta.state) {
          if (downloadDelta.state.current === 'complete') {
            console.log('文件下载完成');
            // 清理临时URL
            if (data.url.startsWith('blob:')) {
              URL.revokeObjectURL(data.url);
            }
          } else if (downloadDelta.state.current === 'interrupted') {
            console.error('文件下载失败');
          }
        }
      });
      
    } catch (error) {
      console.error('下载文件失败:', error);
      throw error;
    }
  }
  
  async handleYoudaoPageLoad(tabId, tab) {
    try {
      // 页面加载完成后，注入必要的样式和脚本
      console.log('检测到有道云笔记页面:', tab.url);
      
      // 可以在这里添加一些页面加载后的初始化逻辑
      // 比如检查页面结构，准备导出环境等
      
    } catch (error) {
      console.error('处理有道云笔记页面加载失败:', error);
    }
  }
  
  // 获取当前活动标签页
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab;
  }
  
  // 检查标签页是否为有道云笔记页面
  isYoudaoNotePage(url) {
    return url && url.includes('note.youdao.com');
  }
}

// 初始化后台脚本
const youdaoBackground = new YoudaoExportBackground();
