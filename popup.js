// popup.js - 插件弹窗逻辑
class YoudaoExportPopup {
  constructor() {
    this.exportBtn = document.getElementById('export-btn');
    this.statusDiv = document.getElementById('status');
    this.folderInfo = document.getElementById('folder-info');
    this.folderName = document.getElementById('folder-name');
    this.noteCount = document.getElementById('note-count');
    this.exportFormat = document.getElementById('export-format');
    this.progress = document.getElementById('progress');
    this.progressFill = document.getElementById('progress-fill');
    this.progressText = document.getElementById('progress-text');
    
    this.init();
  }
  
  async init() {
    // 检查当前页面是否为有道云笔记
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (!tab.url.includes('note.youdao.com')) {
      this.showStatus('请在有道云笔记网页版中使用此插件', 'error');
      this.exportBtn.disabled = true;
      return;
    }
    
    // 获取页面信息
    this.checkPageInfo();
    
    // 绑定事件
    this.exportBtn.addEventListener('click', () => this.startExport());
  }
  
  async checkPageInfo() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // 向content script发送消息获取页面信息
      const response = await chrome.tabs.sendMessage(tab.id, { 
        action: 'getPageInfo' 
      });
      
      if (response && response.success) {
        this.folderName.textContent = response.folderName || '未知文件夹';
        this.noteCount.textContent = response.noteCount || '0';
        this.folderInfo.style.display = 'block';
        
        if (response.noteCount > 0) {
          this.showStatus('检测到笔记，可以开始导出', 'success');
        } else {
          this.showStatus('当前文件夹没有笔记', 'warning');
          this.exportBtn.disabled = true;
        }
      } else {
        this.showStatus('无法获取页面信息，请刷新页面后重试', 'error');
        this.exportBtn.disabled = true;
      }
    } catch (error) {
      console.error('检查页面信息失败:', error);
      this.showStatus('页面检测失败，请确保在有道云笔记页面中使用', 'error');
      this.exportBtn.disabled = true;
    }
  }
  
  async startExport() {
    this.exportBtn.disabled = true;
    this.progress.style.display = 'block';
    this.showStatus('正在导出...', 'warning');
    
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      const format = this.exportFormat.value;
      
      // 开始导出过程
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'startExport',
        format: format
      });
      
      if (response && response.success) {
        // 监听导出进度
        this.listenToProgress();
      } else {
        throw new Error(response?.error || '导出失败');
      }
    } catch (error) {
      console.error('导出失败:', error);
      this.showStatus('导出失败: ' + error.message, 'error');
      this.exportBtn.disabled = false;
      this.progress.style.display = 'none';
    }
  }
  
  listenToProgress() {
    // 监听来自content script的进度更新
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'exportProgress') {
        this.updateProgress(message.progress, message.text);
      } else if (message.action === 'exportComplete') {
        this.handleExportComplete(message.data);
      } else if (message.action === 'exportError') {
        this.handleExportError(message.error);
      }
    });
  }
  
  updateProgress(progress, text) {
    this.progressFill.style.width = progress + '%';
    this.progressText.textContent = text;
  }
  
  async handleExportComplete(data) {
    this.updateProgress(100, '导出完成！');
    this.showStatus('导出成功！正在下载文件...', 'success');
    
    // 创建下载
    const blob = new Blob([data.content], { type: data.mimeType });
    const url = URL.createObjectURL(blob);
    
    try {
      await chrome.downloads.download({
        url: url,
        filename: data.filename,
        saveAs: true
      });
      
      this.showStatus('文件下载完成！', 'success');
    } catch (error) {
      console.error('下载失败:', error);
      this.showStatus('下载失败，请检查浏览器设置', 'error');
    } finally {
      URL.revokeObjectURL(url);
      this.exportBtn.disabled = false;
      setTimeout(() => {
        this.progress.style.display = 'none';
      }, 2000);
    }
  }
  
  handleExportError(error) {
    console.error('导出错误:', error);
    this.showStatus('导出失败: ' + error, 'error');
    this.exportBtn.disabled = false;
    this.progress.style.display = 'none';
  }
  
  showStatus(message, type) {
    this.statusDiv.textContent = message;
    this.statusDiv.className = `status ${type}`;
    this.statusDiv.style.display = 'block';
  }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
  new YoudaoExportPopup();
});
