# 有道云笔记导出助手

一个Chrome浏览器插件，帮助您一键导出有道云笔记网页版某个文件夹下的所有内容。

## 功能特点

- 🚀 **一键导出**: 快速导出整个文件夹下的所有笔记
- 📝 **多种格式**: 支持Markdown、HTML、纯文本、JSON等导出格式
- 📊 **进度显示**: 实时显示导出进度和状态
- 🎯 **智能识别**: 自动识别有道云笔记页面结构
- 💾 **本地保存**: 导出的文件直接保存到本地
- 🔒 **隐私安全**: 所有处理都在本地进行，不上传任何数据

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展程序管理页面（`chrome://extensions/`）
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 插件安装完成！

### 方法二：打包安装

1. 在扩展程序管理页面点击"打包扩展程序"
2. 选择项目文件夹，生成.crx文件
3. 将.crx文件拖拽到Chrome扩展程序页面进行安装

## 使用方法

1. **打开有道云笔记**: 在Chrome浏览器中访问 [有道云笔记网页版](https://note.youdao.com/)
2. **进入目标文件夹**: 导航到您想要导出的文件夹
3. **启动插件**: 点击浏览器工具栏中的插件图标
4. **选择格式**: 在弹出窗口中选择导出格式
5. **开始导出**: 点击"开始导出"按钮
6. **等待完成**: 插件会显示导出进度，完成后自动下载文件

## 支持的导出格式

- **Markdown (.md)**: 适合技术文档和笔记整理
- **HTML (.html)**: 保持格式的网页文件
- **纯文本 (.txt)**: 简洁的文本格式
- **JSON (.json)**: 结构化数据格式，便于程序处理

## 注意事项

1. **页面要求**: 请确保在有道云笔记网页版中使用此插件
2. **网络连接**: 导出过程需要稳定的网络连接
3. **浏览器权限**: 插件需要访问有道云笔记网站的权限
4. **内容限制**: 某些特殊格式的内容可能无法完美导出
5. **导出速度**: 导出速度取决于笔记数量和网络状况

## 故障排除

### 插件无法启动
- 确认当前页面是有道云笔记网页版
- 刷新页面后重试
- 检查插件是否正确安装并启用

### 导出失败
- 检查网络连接是否稳定
- 确认文件夹中有可导出的笔记
- 尝试减少单次导出的笔记数量

### 内容不完整
- 某些动态加载的内容可能需要手动展开
- 复杂格式的笔记建议手动复制重要内容
- 图片和附件需要单独保存

## 技术实现

- **Manifest V3**: 使用最新的Chrome扩展API
- **Content Script**: 注入页面脚本进行内容提取
- **Background Service Worker**: 处理后台任务和消息传递
- **Popup Interface**: 提供用户友好的操作界面

## 开发说明

### 项目结构
```
├── manifest.json          # 插件配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 内容脚本
├── content.css            # 注入样式
├── background.js          # 后台脚本
├── icons/                 # 图标文件夹
└── README.md              # 说明文档
```

### 本地开发
1. 修改代码后，在扩展程序页面点击刷新按钮
2. 使用浏览器开发者工具调试
3. 查看控制台输出了解运行状态

## 版本历史

- **v1.0.0**: 初始版本，支持基本的导出功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 免责声明

本插件仅用于个人学习和使用，请遵守有道云笔记的服务条款。作者不对使用本插件造成的任何问题承担责任。
