<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      font-size: 18px;
      color: #333;
      margin: 0;
    }
    
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
      font-size: 14px;
    }
    
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.warning {
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    .folder-info {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .export-options {
      margin-bottom: 20px;
    }
    
    .export-options label {
      display: block;
      margin-bottom: 10px;
      font-size: 14px;
    }
    
    .export-options select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .buttons {
      display: flex;
      gap: 10px;
    }
    
    .btn {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }
    
    .btn-primary {
      background-color: #007bff;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #0056b3;
    }
    
    .btn-primary:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
    
    .progress {
      margin-top: 15px;
      display: none;
    }
    
    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
    }
    
    .progress-fill {
      height: 100%;
      background-color: #28a745;
      width: 0%;
      transition: width 0.3s ease;
    }
    
    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>有道云笔记导出助手</h1>
  </div>
  
  <div id="status" class="status" style="display: none;"></div>
  
  <div id="folder-info" class="folder-info" style="display: none;">
    <strong>当前文件夹：</strong><span id="folder-name">-</span><br>
    <strong>笔记数量：</strong><span id="note-count">-</span>
  </div>
  
  <div class="export-options">
    <label>
      导出格式：
      <select id="export-format">
        <option value="markdown">Markdown (.md)</option>
        <option value="html">HTML (.html)</option>
        <option value="txt">纯文本 (.txt)</option>
        <option value="json">JSON (.json)</option>
      </select>
    </label>
  </div>
  
  <div class="buttons">
    <button id="export-btn" class="btn btn-primary">开始导出</button>
  </div>
  
  <div id="progress" class="progress">
    <div class="progress-bar">
      <div id="progress-fill" class="progress-fill"></div>
    </div>
    <div id="progress-text" class="progress-text">准备中...</div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
