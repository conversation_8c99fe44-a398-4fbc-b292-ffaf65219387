/* content.css - 注入到有道云笔记页面的样式 */

/* 导出进度指示器 */
.youdao-export-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 14px;
  z-index: 10000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.youdao-export-indicator.success {
  background: rgba(40, 167, 69, 0.9);
}

.youdao-export-indicator.error {
  background: rgba(220, 53, 69, 0.9);
}

.youdao-export-indicator.warning {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
}

/* 导出进度条 */
.youdao-export-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(0, 123, 255, 0.3);
  z-index: 10001;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.youdao-export-progress.active {
  transform: scaleX(var(--progress, 0));
}

/* 高亮正在导出的笔记 */
.youdao-exporting-note {
  background-color: rgba(0, 123, 255, 0.1) !important;
  border: 2px solid rgba(0, 123, 255, 0.3) !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

/* 已导出的笔记标记 */
.youdao-exported-note {
  position: relative;
}

.youdao-exported-note::after {
  content: '✓';
  position: absolute;
  top: 5px;
  right: 5px;
  background: #28a745;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 导出失败的笔记标记 */
.youdao-export-failed-note {
  position: relative;
}

.youdao-export-failed-note::after {
  content: '✗';
  position: absolute;
  top: 5px;
  right: 5px;
  background: #dc3545;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 浮动操作按钮 */
.youdao-export-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  background: #007bff;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: all 0.3s ease;
}

.youdao-export-fab:hover {
  background: #0056b3;
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.6);
}

.youdao-export-fab svg {
  width: 24px;
  height: 24px;
  fill: white;
}

/* 导出状态提示 */
.youdao-export-toast {
  position: fixed;
  bottom: 100px;
  right: 30px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  max-width: 300px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.youdao-export-toast.show {
  opacity: 1;
  transform: translateY(0);
}

.youdao-export-toast.success {
  border-left: 4px solid #28a745;
}

.youdao-export-toast.error {
  border-left: 4px solid #dc3545;
}

.youdao-export-toast.warning {
  border-left: 4px solid #ffc107;
}

.youdao-export-toast .toast-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.youdao-export-toast .toast-message {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .youdao-export-indicator {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .youdao-export-fab {
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
  }
  
  .youdao-export-fab svg {
    width: 20px;
    height: 20px;
  }
  
  .youdao-export-toast {
    bottom: 80px;
    right: 20px;
    max-width: 250px;
    padding: 12px;
  }
}

/* 动画效果 */
@keyframes youdao-export-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.youdao-export-indicator.pulse {
  animation: youdao-export-pulse 1s infinite;
}

@keyframes youdao-export-fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.youdao-export-indicator {
  animation: youdao-export-fadeIn 0.3s ease;
}

/* 确保不影响原页面样式 */
.youdao-export-indicator,
.youdao-export-progress,
.youdao-export-fab,
.youdao-export-toast {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  box-sizing: border-box !important;
}
