// content.js - 有道云笔记页面内容脚本
class YoudaoNoteExporter {
  constructor() {
    this.notes = [];
    this.currentFolder = '';
    this.isExporting = false;
    
    this.init();
  }
  
  init() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
    
    console.log('有道云笔记导出助手已加载');
  }
  
  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'getPageInfo':
          const pageInfo = await this.getPageInfo();
          sendResponse(pageInfo);
          break;
          
        case 'startExport':
          const exportResult = await this.startExport(message.format);
          sendResponse(exportResult);
          break;
          
        default:
          sendResponse({ success: false, error: '未知操作' });
      }
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }
  
  async getPageInfo() {
    try {
      // 等待页面加载完成
      await this.waitForPageLoad();
      
      // 获取当前文件夹名称
      const folderName = this.getCurrentFolderName();
      
      // 获取笔记列表
      const notes = this.getNotesList();
      
      return {
        success: true,
        folderName: folderName,
        noteCount: notes.length,
        notes: notes
      };
    } catch (error) {
      console.error('获取页面信息失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  async waitForPageLoad() {
    // 等待页面主要元素加载完成
    const maxWait = 10000; // 最多等待10秒
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWait) {
      // 检查是否有笔记列表容器
      const noteList = document.querySelector('.file-list') || 
                      document.querySelector('[data-testid="note-list"]') ||
                      document.querySelector('.note-list') ||
                      document.querySelector('.list-container');
      
      if (noteList) {
        // 再等待一点时间确保内容加载完成
        await this.sleep(1000);
        return;
      }
      
      await this.sleep(500);
    }
    
    throw new Error('页面加载超时，请刷新页面后重试');
  }
  
  getCurrentFolderName() {
    // 尝试多种方式获取当前文件夹名称
    const selectors = [
      '.breadcrumb .active',
      '.folder-name',
      '.current-folder',
      '.nav-breadcrumb .active',
      'h1.folder-title',
      '.sidebar .selected .folder-name'
    ];
    
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        return element.textContent.trim();
      }
    }
    
    // 从URL中提取文件夹信息
    const urlMatch = window.location.href.match(/\/folder\/([^\/]+)/);
    if (urlMatch) {
      return decodeURIComponent(urlMatch[1]);
    }
    
    return '根目录';
  }
  
  getNotesList() {
    const notes = [];
    
    // 尝试多种选择器来找到笔记列表
    const noteSelectors = [
      '.file-list .file-item',
      '.note-list .note-item',
      '.list-container .list-item',
      '[data-testid="note-item"]',
      '.note-card'
    ];
    
    let noteElements = [];
    for (const selector of noteSelectors) {
      noteElements = document.querySelectorAll(selector);
      if (noteElements.length > 0) break;
    }
    
    noteElements.forEach((element, index) => {
      const noteInfo = this.extractNoteInfo(element, index);
      if (noteInfo) {
        notes.push(noteInfo);
      }
    });
    
    return notes;
  }
  
  extractNoteInfo(element, index) {
    try {
      // 尝试多种方式获取笔记标题
      const titleSelectors = [
        '.file-name',
        '.note-title',
        '.title',
        '.name',
        'a[title]',
        '.file-item-name'
      ];
      
      let title = '';
      for (const selector of titleSelectors) {
        const titleElement = element.querySelector(selector);
        if (titleElement) {
          title = titleElement.textContent.trim() || titleElement.getAttribute('title') || '';
          if (title) break;
        }
      }
      
      // 获取笔记链接
      const linkElement = element.querySelector('a') || element;
      const href = linkElement.getAttribute('href') || linkElement.getAttribute('data-href') || '';
      
      // 获取更新时间
      const timeSelectors = ['.update-time', '.time', '.date', '.modified'];
      let updateTime = '';
      for (const selector of timeSelectors) {
        const timeElement = element.querySelector(selector);
        if (timeElement) {
          updateTime = timeElement.textContent.trim();
          break;
        }
      }
      
      if (!title) {
        title = `未命名笔记_${index + 1}`;
      }
      
      return {
        title: title,
        href: href,
        updateTime: updateTime,
        element: element
      };
    } catch (error) {
      console.error('提取笔记信息失败:', error);
      return null;
    }
  }
  
  async startExport(format) {
    if (this.isExporting) {
      return { success: false, error: '正在导出中，请稍候' };
    }
    
    this.isExporting = true;
    
    try {
      // 获取所有笔记
      const pageInfo = await this.getPageInfo();
      if (!pageInfo.success) {
        throw new Error(pageInfo.error);
      }
      
      const notes = pageInfo.notes;
      if (notes.length === 0) {
        throw new Error('没有找到可导出的笔记');
      }
      
      // 开始导出过程
      this.sendProgress(0, '开始导出...');
      
      const exportedNotes = [];
      for (let i = 0; i < notes.length; i++) {
        const note = notes[i];
        this.sendProgress(
          Math.round((i / notes.length) * 80), 
          `正在导出: ${note.title} (${i + 1}/${notes.length})`
        );
        
        try {
          const content = await this.extractNoteContent(note);
          exportedNotes.push({
            title: note.title,
            content: content,
            updateTime: note.updateTime
          });
        } catch (error) {
          console.error(`导出笔记失败: ${note.title}`, error);
          exportedNotes.push({
            title: note.title,
            content: `导出失败: ${error.message}`,
            updateTime: note.updateTime,
            error: true
          });
        }
        
        // 避免请求过快
        await this.sleep(500);
      }
      
      this.sendProgress(90, '正在生成导出文件...');
      
      // 生成导出文件
      const exportData = this.generateExportFile(exportedNotes, format, pageInfo.folderName);
      
      this.sendProgress(100, '导出完成！');
      
      // 发送完成消息
      chrome.runtime.sendMessage({
        action: 'exportComplete',
        data: exportData
      });
      
      return { success: true };
      
    } catch (error) {
      console.error('导出失败:', error);
      chrome.runtime.sendMessage({
        action: 'exportError',
        error: error.message
      });
      return { success: false, error: error.message };
    } finally {
      this.isExporting = false;
    }
  }
  
  async extractNoteContent(note) {
    // 这里需要根据有道云笔记的实际页面结构来提取内容
    // 由于有道云笔记可能使用AJAX加载内容，我们需要模拟点击或发送请求
    
    try {
      // 方法1: 尝试直接从当前页面获取内容（如果笔记已经展开）
      const contentFromPage = this.tryGetContentFromCurrentPage(note);
      if (contentFromPage) {
        return contentFromPage;
      }
      
      // 方法2: 尝试通过点击笔记来获取内容
      const contentFromClick = await this.tryGetContentByClick(note);
      if (contentFromClick) {
        return contentFromClick;
      }
      
      // 方法3: 返回基本信息
      return `标题: ${note.title}\n更新时间: ${note.updateTime}\n\n[内容无法自动提取，请手动复制]`;
      
    } catch (error) {
      console.error('提取笔记内容失败:', error);
      return `标题: ${note.title}\n更新时间: ${note.updateTime}\n\n[提取失败: ${error.message}]`;
    }
  }
  
  tryGetContentFromCurrentPage(note) {
    // 尝试从当前页面直接获取笔记内容
    const contentSelectors = [
      '.note-content',
      '.editor-content',
      '.content',
      '.note-body',
      '[data-testid="note-content"]'
    ];
    
    for (const selector of contentSelectors) {
      const contentElement = document.querySelector(selector);
      if (contentElement) {
        return this.extractTextContent(contentElement);
      }
    }
    
    return null;
  }
  
  async tryGetContentByClick(note) {
    // 尝试通过点击笔记来获取内容
    if (!note.element) return null;
    
    try {
      // 点击笔记
      const clickableElement = note.element.querySelector('a') || note.element;
      clickableElement.click();
      
      // 等待内容加载
      await this.sleep(2000);
      
      // 尝试获取内容
      return this.tryGetContentFromCurrentPage(note);
      
    } catch (error) {
      console.error('通过点击获取内容失败:', error);
      return null;
    }
  }
  
  extractTextContent(element) {
    // 提取元素的文本内容，保持基本格式
    let content = '';
    
    // 递归提取文本，保持段落结构
    const extractText = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        return node.textContent;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const tagName = node.tagName.toLowerCase();
        let text = '';
        
        // 处理不同的HTML标签
        switch (tagName) {
          case 'br':
            return '\n';
          case 'p':
          case 'div':
          case 'h1':
          case 'h2':
          case 'h3':
          case 'h4':
          case 'h5':
          case 'h6':
            for (const child of node.childNodes) {
              text += extractText(child);
            }
            return text + '\n\n';
          case 'li':
            for (const child of node.childNodes) {
              text += extractText(child);
            }
            return '• ' + text + '\n';
          default:
            for (const child of node.childNodes) {
              text += extractText(child);
            }
            return text;
        }
      }
      return '';
    };
    
    content = extractText(element);
    
    // 清理多余的空行
    content = content.replace(/\n{3,}/g, '\n\n').trim();
    
    return content;
  }
  
  generateExportFile(notes, format, folderName) {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    let content = '';
    let filename = '';
    let mimeType = '';
    
    switch (format) {
      case 'markdown':
        content = this.generateMarkdown(notes, folderName);
        filename = `${folderName}_导出_${timestamp}.md`;
        mimeType = 'text/markdown';
        break;
        
      case 'html':
        content = this.generateHTML(notes, folderName);
        filename = `${folderName}_导出_${timestamp}.html`;
        mimeType = 'text/html';
        break;
        
      case 'txt':
        content = this.generateText(notes, folderName);
        filename = `${folderName}_导出_${timestamp}.txt`;
        mimeType = 'text/plain';
        break;
        
      case 'json':
        content = this.generateJSON(notes, folderName);
        filename = `${folderName}_导出_${timestamp}.json`;
        mimeType = 'application/json';
        break;
        
      default:
        throw new Error('不支持的导出格式');
    }
    
    return {
      content: content,
      filename: filename,
      mimeType: mimeType
    };
  }
  
  generateMarkdown(notes, folderName) {
    let content = `# ${folderName} - 导出\n\n`;
    content += `导出时间: ${new Date().toLocaleString()}\n`;
    content += `笔记数量: ${notes.length}\n\n`;
    content += '---\n\n';
    
    notes.forEach((note, index) => {
      content += `## ${index + 1}. ${note.title}\n\n`;
      if (note.updateTime) {
        content += `**更新时间:** ${note.updateTime}\n\n`;
      }
      if (note.error) {
        content += `> ⚠️ ${note.content}\n\n`;
      } else {
        content += `${note.content}\n\n`;
      }
      content += '---\n\n';
    });
    
    return content;
  }
  
  generateHTML(notes, folderName) {
    let content = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${folderName} - 导出</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .meta { color: #666; font-size: 0.9em; margin-bottom: 15px; }
        .note { margin-bottom: 40px; padding: 20px; border: 1px solid #eee; border-radius: 5px; }
        .error { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>${folderName} - 导出</h1>
    <div class="meta">
        <p>导出时间: ${new Date().toLocaleString()}</p>
        <p>笔记数量: ${notes.length}</p>
    </div>
`;
    
    notes.forEach((note, index) => {
      const errorClass = note.error ? ' error' : '';
      content += `    <div class="note${errorClass}">
        <h2>${index + 1}. ${note.title}</h2>`;
      
      if (note.updateTime) {
        content += `        <div class="meta">更新时间: ${note.updateTime}</div>`;
      }
      
      content += `        <pre>${note.content}</pre>
    </div>
`;
    });
    
    content += `</body>
</html>`;
    
    return content;
  }
  
  generateText(notes, folderName) {
    let content = `${folderName} - 导出\n`;
    content += '='.repeat(folderName.length + 5) + '\n\n';
    content += `导出时间: ${new Date().toLocaleString()}\n`;
    content += `笔记数量: ${notes.length}\n\n`;
    
    notes.forEach((note, index) => {
      content += `${index + 1}. ${note.title}\n`;
      content += '-'.repeat(note.title.length + 4) + '\n';
      if (note.updateTime) {
        content += `更新时间: ${note.updateTime}\n`;
      }
      content += `\n${note.content}\n\n`;
      content += '='.repeat(50) + '\n\n';
    });
    
    return content;
  }
  
  generateJSON(notes, folderName) {
    const data = {
      folderName: folderName,
      exportTime: new Date().toISOString(),
      noteCount: notes.length,
      notes: notes
    };
    
    return JSON.stringify(data, null, 2);
  }
  
  sendProgress(progress, text) {
    chrome.runtime.sendMessage({
      action: 'exportProgress',
      progress: progress,
      text: text
    });
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 初始化导出器
const youdaoExporter = new YoudaoNoteExporter();
